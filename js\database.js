// Optimized Database Manager for Aanabi Pharmacy Management System v2.0
class DatabaseManager {
    constructor() {
        this.db = null;
        this.isInitialized = false;
        this.currentUser = null;
        this.cache = new Map(); // Performance: Add caching layer
        this.migrationCompleted = false;
        this.init();
    }

    isReady() {
        return this.isInitialized && this.db !== null;
    }

    // Optimized initialization with better performance
    async init() {
        try {
            console.log('🚀 Starting optimized database initialization...');
            
            // Quick database opening
            this.db = await this.openDatabase();
            console.log('✅ Database opened successfully');

            // Check migration status first to avoid unnecessary work
            this.migrationCompleted = await this.checkMigrationStatus();
            
            // Only run migration if needed
            if (!this.migrationCompleted) {
                console.log('🔄 Running one-time data migration...');
                await this.performOptimizedMigration();
                await this.markMigrationComplete();
            }

            // Insert minimal sample data only if needed
            await this.insertMinimalSampleData();

            // Start background services
            this.startBackgroundServices();

            this.isInitialized = true;
            console.log('🎉 Optimized database initialized successfully');
        } catch (error) {
            console.error('❌ Database initialization failed:', error);
            this.db = null;
            this.isInitialized = false;
            this.showUserFriendlyError(error);
        }
    }

    async openDatabase() {
        return new Promise((resolve, reject) => {
            if (!window.indexedDB) {
                reject(new Error('IndexedDB is not supported in this browser'));
                return;
            }

            const request = indexedDB.open('AanabiPharmacyDB', 5);

            request.onerror = () => {
                console.error('IndexedDB open error:', request.error);
                reject(request.error || new Error('Failed to open database'));
            };

            request.onsuccess = () => {
                console.log('IndexedDB opened successfully');
                resolve(request.result);
            };

            request.onupgradeneeded = (event) => {
                console.log('🔄 Database upgrade needed, creating object stores...');
                const db = event.target.result;
                this.createObjectStores(db);
            };
        });
    }

    createObjectStores(db) {
        console.log('📊 Creating optimized object stores...');

        // Create all necessary object stores with optimized indexes
        const stores = [
            {
                name: 'medicines',
                keyPath: 'medicine_id',
                indexes: [
                    { name: 'name', field: 'medicine_name', unique: false },
                    { name: 'category', field: 'category', unique: false },
                    { name: 'status', field: 'status', unique: false }
                ]
            },
            {
                name: 'customers',
                keyPath: 'customer_id',
                indexes: [
                    { name: 'phone', field: 'phone_number', unique: true },
                    { name: 'name', field: 'customer_name', unique: false },
                    { name: 'status', field: 'status', unique: false }
                ]
            },
            {
                name: 'sales',
                keyPath: 'sale_id',
                indexes: [
                    { name: 'date', field: 'date', unique: false },
                    { name: 'customer_phone', field: 'customer_phone', unique: false },
                    { name: 'medicine_id', field: 'medicine_id', unique: false }
                ]
            },
            {
                name: 'purchases',
                keyPath: 'purchase_id',
                indexes: [
                    { name: 'supplier_name', field: 'supplier_name', unique: false },
                    { name: 'date', field: 'date', unique: false },
                    { name: 'medicine_id', field: 'medicine_id', unique: false }
                ]
            },
            {
                name: 'inventory',
                keyPath: 'inventory_id',
                indexes: [
                    { name: 'medicine_id', field: 'medicine_id', unique: false },
                    { name: 'expiry_date', field: 'expiry_date', unique: false },
                    { name: 'batch_number', field: 'batch_number', unique: false }
                ]
            },
            {
                name: 'suppliers',
                keyPath: 'supplier_id',
                indexes: [
                    { name: 'name', field: 'supplier_name', unique: false },
                    { name: 'status', field: 'status', unique: false }
                ]
            },
            {
                name: 'settings',
                keyPath: 'key',
                indexes: []
            },
            // Users table removed - no authentication system
            {
                name: 'audit_logs',
                keyPath: 'log_id',
                indexes: [
                    { name: 'timestamp', field: 'timestamp', unique: false },
                    { name: 'user_id', field: 'user_id', unique: false },
                    { name: 'action', field: 'action', unique: false }
                ]
            },
            {
                name: 'notifications',
                keyPath: 'notification_id',
                indexes: [
                    { name: 'user_id', field: 'user_id', unique: false },
                    { name: 'type', field: 'type', unique: false },
                    { name: 'created_date', field: 'created_date', unique: false }
                ]
            }
        ];

        stores.forEach(storeConfig => {
            if (!db.objectStoreNames.contains(storeConfig.name)) {
                const store = db.createObjectStore(storeConfig.name, { keyPath: storeConfig.keyPath });
                storeConfig.indexes.forEach(index => {
                    store.createIndex(index.name, index.field, { unique: index.unique });
                });
                console.log(`✅ Created ${storeConfig.name} store`);
            }
        });
    }

    // Optimized migration check using settings table
    async checkMigrationStatus() {
        try {
            const migrationFlag = await this.get('settings', 'migration_v2_completed');
            return migrationFlag && migrationFlag.value === 'true';
        } catch (error) {
            return false;
        }
    }

    async markMigrationComplete() {
        try {
            await this.insert('settings', { key: 'migration_v2_completed', value: 'true' });
            this.migrationCompleted = true;
        } catch (error) {
            console.warn('Could not mark migration as complete:', error);
        }
    }

    // Streamlined migration process
    async performOptimizedMigration() {
        try {
            console.log('🔄 Starting optimized migration...');
            
            // Migrate existing data in batches for better performance
            await this.migrateMedicinesOptimized();
            await this.migrateCustomersOptimized();
            await this.migrateSalesOptimized();
            await this.insertEssentialSettings();
            
            console.log('✅ Optimized migration completed');
        } catch (error) {
            console.error('❌ Migration failed:', error);
            throw error;
        }
    }

    async migrateMedicinesOptimized() {
        const medicines = await this.getAll('medicines');
        if (medicines.length === 0) return;
        
        // Batch update for better performance
        const updatePromises = medicines.map(medicine => {
            if (!medicine.hasOwnProperty('regular_stock')) {
                medicine.regular_stock = medicine.current_stock || 0;
                medicine.bonus_stock = 0;
                medicine.loyalty_point_price = null;
                medicine.total_bonus_received = 0;
                medicine.last_bonus_date = null;
                return this.updateWithoutAudit('medicines', medicine);
            }
            return Promise.resolve();
        });
        
        await Promise.all(updatePromises);
        console.log(`✅ Optimized migration of ${medicines.length} medicines`);
    }

    async migrateCustomersOptimized() {
        const customers = await this.getAll('customers');
        if (customers.length === 0) return;
        
        const updatePromises = customers.map(customer => {
            if (!customer.hasOwnProperty('medical_history')) {
                customer.medical_history = '';
                customer.diagnosis = '';
                customer.adr_history = '';
                return this.updateWithoutAudit('customers', customer);
            }
            return Promise.resolve();
        });
        
        await Promise.all(updatePromises);
        console.log(`✅ Optimized migration of ${customers.length} customers`);
    }

    async migrateSalesOptimized() {
        const sales = await this.getAll('sales');
        if (sales.length === 0) return;
        
        const updatePromises = sales.map(sale => {
            if (!sale.hasOwnProperty('bonus_qty_sold')) {
                sale.bonus_qty_sold = 0;
                sale.regular_qty_sold = sale.quantity || 0;
                sale.loyalty_points_used = 0;
                sale.loyalty_points_value = 0.00;
                sale.cash_amount = sale.final_total || sale.total_amount || 0;
                sale.bonus_profit = 0.00;
                sale.regular_profit = sale.profit_amount || 0.00;
                sale.total_profit = sale.profit_amount || 0.00;
                sale.tax_system_used = 'vat';
                return this.updateWithoutAudit('sales', sale);
            }
            return Promise.resolve();
        });
        
        await Promise.all(updatePromises);
        console.log(`✅ Optimized migration of ${sales.length} sales`);
    }

    // Insert only essential sample data to reduce startup time
    async insertMinimalSampleData() {
        try {
            // Check if any data exists
            const medicines = await this.getAll('medicines');

            if (medicines.length === 0) {
                await this.insertEssentialSampleData();
            }
            
            console.log('✅ Essential sample data ready');
        } catch (error) {
            console.error('❌ Error inserting sample data:', error);
        }
    }

    // User creation removed - no authentication system needed

    async insertEssentialSampleData() {
        // Insert only 2-3 essential medicines instead of large dataset
        const essentialMedicines = [
            {
                medicine_id: 'MED001',
                medicine_name: 'Paracetamol 500mg',
                generic_name: 'Acetaminophen',
                category: 'Pain Relief',
                manufacturer: 'ABC Pharma',
                pack_size: '10',
                unit_type: 'Tablets',
                min_stock_level: 50,
                regular_stock: 100,
                bonus_stock: 20,
                current_stock: 120,
                purchase_price: 10.00,
                selling_price: 20.00,
                margin_percent: 50.0,
                storage_location: 'A-1',
                prescription_required: 'No',
                status: 'Active'
            },
            {
                medicine_id: 'MED002',
                medicine_name: 'Amoxicillin 250mg',
                generic_name: 'Amoxicillin',
                category: 'Antibiotic',
                manufacturer: 'XYZ Labs',
                pack_size: '10',
                unit_type: 'Capsules',
                min_stock_level: 30,
                regular_stock: 60,
                bonus_stock: 15,
                current_stock: 75,
                purchase_price: 15.00,
                selling_price: 25.00,
                margin_percent: 40.0,
                storage_location: 'B-2',
                prescription_required: 'Yes',
                status: 'Active'
            }
        ];

        // Insert essential data without audit logging for performance
        for (const medicine of essentialMedicines) {
            try {
                await this.insertWithoutAudit('medicines', medicine);
            } catch (error) {
                console.warn('Medicine already exists:', medicine.medicine_id);
            }
        }

        // Insert one sample customer and sale
        const sampleCustomer = {
            customer_id: 'CUST001',
            customer_name: 'John Doe',
            phone_number: '**********',
            email: '<EMAIL>',
            address: 'Kathmandu, Nepal',
            medical_history: '',
            diagnosis: '',
            adr_history: '',
            allergies: '',
            registration_date: new Date().toISOString().split('T')[0],
            loyalty_points: 0,
            status: 'Active'
        };

        const sampleSale = {
            sale_id: 'SALE001',
            date: new Date().toISOString().split('T')[0],
            time: '10:30:00',
            customer_name: 'John Doe',
            customer_phone: '**********',
            medicine_id: 'MED001',
            medicine_name: 'Paracetamol 500mg',
            quantity: 2,
            bonus_qty_sold: 2,
            regular_qty_sold: 0,
            unit_price: 20.00,
            total_amount: 40.00,
            payment_method: 'Cash',
            bonus_profit: 40.00,
            regular_profit: 0.00,
            total_profit: 40.00
        };

        try {
            await this.insertWithoutAudit('customers', sampleCustomer);
            await this.insertWithoutAudit('sales', sampleSale);
        } catch (error) {
            console.warn('Sample data already exists');
        }
        
        console.log('✅ Essential sample data created');
    }

    async insertEssentialSettings() {
        const essentialSettings = [
            { key: 'pharmacy_name', value: 'Aanabi Pharmacy' },
            { key: 'tax_system', value: 'vat' },
            { key: 'vat_rate', value: '13' },
            { key: 'loyalty_points_rate', value: '20' },
            { key: 'loyalty_redemption_rate', value: '0.10' },
            { key: 'default_margin', value: '60' }
        ];

        for (const setting of essentialSettings) {
            try {
                const existing = await this.get('settings', setting.key);
                if (!existing) {
                    await this.insertWithoutAudit('settings', setting);
                }
            } catch (error) {
                await this.insertWithoutAudit('settings', setting);
            }
        }
        
        console.log('✅ Essential settings created');
    }

    // Optimized CRUD operations with caching
    async insert(storeName, data) {
        if (!this.db) {
            throw new Error('Database not initialized');
        }
        
        const result = await this.insertWithoutAudit(storeName, data);
        
        // Clear cache for this store
        this.cache.delete(storeName);
        
        // Log the action (async to not block)
        this.logAuditTrail('CREATE', storeName, data, 'Record created').catch(console.warn);
        
        return result;
    }

    async update(storeName, data) {
        if (!this.db) {
            throw new Error('Database not initialized');
        }
        
        const result = await this.updateWithoutAudit(storeName, data);
        
        // Clear cache for this store
        this.cache.delete(storeName);
        
        // Log the action (async to not block)
        this.logAuditTrail('UPDATE', storeName, data, 'Record updated').catch(console.warn);
        
        return result;
    }

    async delete(storeName, key) {
        if (!this.db) {
            throw new Error('Database not initialized');
        }
        
        const result = await new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.delete(key);

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
        
        // Clear cache for this store
        this.cache.delete(storeName);
        
        // Log the action (async to not block)
        this.logAuditTrail('DELETE', storeName, { key: key }, 'Record deleted').catch(console.warn);
        
        return result;
    }

    // Performance: Direct operations without audit logging
    async insertWithoutAudit(storeName, data) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.add(data);

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async updateWithoutAudit(storeName, data) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.put(data);

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async get(storeName, key) {
        if (!this.db) {
            throw new Error('Database not initialized');
        }
        
        // Check cache first
        const cacheKey = `${storeName}_${key}`;
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }
        
        const result = await new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.get(key);

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
        
        // Cache the result
        if (result) {
            this.cache.set(cacheKey, result);
        }
        
        return result;
    }

    async getAll(storeName) {
        if (!this.db) {
            throw new Error('Database not initialized');
        }
        
        // Check cache first
        if (this.cache.has(storeName)) {
            return this.cache.get(storeName);
        }
        
        const result = await new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.getAll();

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
        
        // Cache the result for 30 seconds
        this.cache.set(storeName, result);
        setTimeout(() => this.cache.delete(storeName), 30000);
        
        return result;
    }

    async search(storeName, indexName, value) {
        if (!this.db) {
            throw new Error('Database not initialized');
        }
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const index = store.index(indexName);
            const request = index.getAll(value);

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async generateId(storeName, prefix) {
        if (!this.db) {
            throw new Error('Database not initialized');
        }
        const allRecords = await this.getAll(storeName);
        const maxId = allRecords.reduce((max, record) => {
            const idField = Object.keys(record)[0];
            const idNumber = parseInt(record[idField].substring(prefix.length));
            return Math.max(max, idNumber);
        }, 0);
        return prefix + String(maxId + 1).padStart(3, '0');
    }

    // Optimized audit logging (async and batched)
    async logAuditTrail(action, module, data, reason = '', oldData = null) {
        try {
            if (!this.currentUser) {
                return; // Skip if no user logged in
            }

            const auditRecord = {
                log_id: await this.generateId('audit_logs', 'LOG'),
                timestamp: new Date().toISOString(),
                user_id: this.currentUser.user_id,
                user_name: this.currentUser.username,
                action: action,
                module: module,
                record_id: data.key || data[Object.keys(data)[0]] || 'unknown',
                reason: reason
            };

            // Insert without blocking main operations
            const transaction = this.db.transaction(['audit_logs'], 'readwrite');
            const store = transaction.objectStore('audit_logs');
            store.add(auditRecord);
        } catch (error) {
            console.warn('Audit logging failed:', error);
        }
    }

    // Notification management (simplified)
    async createNotification(notificationData) {
        try {
            const notification = {
                notification_id: await this.generateId('notifications', 'NOT'),
                created_date: new Date().toISOString(),
                read_status: false,
                expires_at: this.calculateExpiryDate(7),
                status: 'Active',
                ...notificationData
            };

            await this.insertWithoutAudit('notifications', notification);
            return notification;
        } catch (error) {
            console.error('Failed to create notification:', error);
            throw error;
        }
    }

    calculateExpiryDate(days) {
        const expiryDate = new Date();
        expiryDate.setDate(expiryDate.getDate() + days);
        return expiryDate.toISOString();
    }

    // Optimized background services
    startBackgroundServices() {
        // Run cleanup once per hour instead of continuous
        setInterval(() => {
            this.cleanupExpiredNotifications().catch(console.warn);
        }, 60 * 60 * 1000); // 1 hour
        
        // Clear cache periodically
        setInterval(() => {
            this.cache.clear();
        }, 10 * 60 * 1000); // 10 minutes
    }

    async cleanupExpiredNotifications() {
        try {
            const notifications = await this.getAll('notifications');
            const now = new Date();
            let cleanedCount = 0;

            for (const notification of notifications) {
                const expiryDate = new Date(notification.expires_at);
                if (expiryDate < now) {
                    await this.delete('notifications', notification.notification_id);
                    cleanedCount++;
                }
            }

            if (cleanedCount > 0) {
                console.log(`🗑️ Cleaned up ${cleanedCount} expired notifications`);
            }
        } catch (error) {
            console.error('Failed to cleanup notifications:', error);
        }
    }

    // User authentication helpers
    setCurrentUser(user) {
        this.currentUser = user;
    }

    getCurrentUser() {
        return this.currentUser;
    }

    // Improved error handling
    showUserFriendlyError(error) {
        setTimeout(() => {
            if (!this.isInitialized) {
                const message = error.message.includes('IndexedDB') 
                    ? 'Database storage is not available in your browser. Please enable IndexedDB or try a different browser.'
                    : 'System initialization failed. Please refresh the page to try again.';
                
                if (window.showAlert) {
                    window.showAlert(message, 'danger');
                } else {
                    alert(message);
                }
            }
        }, 2000);
    }

    // Quick database reset (for troubleshooting)
    async clearDatabase() {
        return new Promise((resolve, reject) => {
            if (this.db) {
                this.db.close();
                this.db = null;
                this.isInitialized = false;
            }

            this.cache.clear();
            const deleteRequest = indexedDB.deleteDatabase('AanabiPharmacyDB');

            deleteRequest.onsuccess = () => {
                console.log('Database cleared successfully');
                resolve();
            };

            deleteRequest.onerror = () => {
                console.error('Failed to clear database:', deleteRequest.error);
                reject(deleteRequest.error);
            };

            deleteRequest.onblocked = () => {
                console.warn('Database clear blocked - other tabs may be using the database');
                reject(new Error('Database clear blocked. Please close other tabs and try again.'));
            };
        });
    }

    // Performance monitoring
    getPerformanceStats() {
        return {
            cacheSize: this.cache.size,
            isInitialized: this.isInitialized,
            migrationCompleted: this.migrationCompleted,
            currentUser: this.currentUser?.username || 'none'
        };
    }
}

// Initialize optimized database
const dbManager = new DatabaseManager();

// Export for global use
if (typeof window !== 'undefined') {
    window.dbManager = dbManager;
    window.dbPerformance = () => console.log(dbManager.getPerformanceStats());
}
