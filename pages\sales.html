<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Sales - Aanabi Pharmacy v2.0</title>
    <link rel="stylesheet" href="../css/style.css">
    <style>
        /* Enhanced Sales Styles */
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
            color: white;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .user-details {
            display: flex;
            flex-direction: column;
            font-size: 0.9rem;
        }

        .user-name {
            font-weight: 600;
        }

        .user-role {
            opacity: 0.8;
            font-size: 0.8rem;
            text-transform: capitalize;
        }

        /* Logout button styles removed - using mode switching instead */

        /* Enhanced Statistics Cards */
        .enhanced-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .enhanced-stat-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid;
            transition: all 0.3s ease;
        }

        .enhanced-stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .enhanced-stat-card.bonus {
            border-left-color: #27ae60;
        }

        .enhanced-stat-card.regular {
            border-left-color: #3498db;
        }

        .enhanced-stat-card.total {
            border-left-color: #f39c12;
        }

        .enhanced-stat-card.loyalty {
            border-left-color: #9b59b6;
        }

        .stat-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }

        .stat-icon {
            font-size: 1.5rem;
        }

        .stat-title {
            font-size: 0.8rem;
            color: #7f8c8d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .enhanced-stat-value {
            font-size: 1.8rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .stat-subtitle {
            font-size: 0.8rem;
            color: #95a5a6;
        }

        /* Customer Search Enhancement */
        .customer-search {
            position: relative;
        }

        .customer-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .customer-suggestion {
            padding: 0.5rem 1rem;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
        }

        .customer-suggestion:hover {
            background: #f8f9fa;
        }

        .customer-suggestion:last-child {
            border-bottom: none;
        }

        /* Medical Alert Styles */
        .medical-alert {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }

        .allergy-warning {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 0.5rem;
            border-radius: 4px;
            margin-top: 0.5rem;
        }

        /* Stock Allocation Display */
        .stock-allocation {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            border: 1px solid #dee2e6;
        }

        .allocation-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e9ecef;
        }

        .allocation-item:last-child {
            border-bottom: none;
        }

        .allocation-type {
            font-weight: 500;
        }

        .allocation-type.bonus {
            color: #27ae60;
        }

        .allocation-type.regular {
            color: #3498db;
        }

        .allocation-qty {
            font-weight: bold;
        }

        /* Loyalty Points Section */
        .loyalty-section {
            background: linear-gradient(135deg, #fff5f5 0%, #ffeaa7 100%);
            border-radius: 12px;
            padding: 1.5rem;
            margin: 1rem 0;
            border: 2px solid #f39c12;
        }

        .loyalty-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .loyalty-header h4 {
            margin: 0;
            color: #d35400;
        }

        .points-display {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            margin: 1rem 0;
        }

        .points-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #d35400;
        }

        .points-label {
            font-size: 0.8rem;
            color: #7f8c8d;
            text-transform: uppercase;
        }

        /* Enhanced Modal Styles */
        .modal-lg {
            max-width: 900px;
        }

        .section-card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid #3498db;
        }

        .section-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
            color: #2c3e50;
        }

        .section-header h4 {
            margin: 0;
        }

        /* Payment Method Enhancement */
        .payment-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .payment-option {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .payment-option:hover {
            background: #e9ecef;
            border-color: #3498db;
        }

        .payment-option.selected {
            background: #3498db;
            color: white;
            border-color: #2980b9;
        }

        .payment-icon {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }

        /* Bill Enhancement */
        .bill-content {
            background: white;
            padding: 2rem;
            font-family: 'Courier New', monospace;
            max-width: 300px;
            margin: 0 auto;
        }

        .bill-header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 1rem;
            margin-bottom: 1rem;
        }

        .bill-row {
            display: flex;
            justify-content: space-between;
            padding: 0.25rem 0;
            border-bottom: 1px dotted #ccc;
        }

        .bill-total {
            font-weight: bold;
            font-size: 1.1rem;
            border-top: 2px solid #333;
            margin-top: 1rem;
            padding-top: 0.5rem;
        }

        /* Permission-based hiding */
        .admin-only {
            display: none;
        }

        body.admin .admin-only {
            display: block;
        }

        body.admin .admin-only.inline {
            display: inline-block;
        }

        body.admin .admin-only.flex {
            display: flex;
        }

        /* Loading states */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            backdrop-filter: blur(5px);
        }

        .loading-content {
            text-align: center;
            color: #2c3e50;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #ecf0f1;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Enhanced Table */
        .sales-table td {
            font-size: 0.9rem;
        }

        .profit-breakdown {
            font-size: 0.8rem;
            color: #7f8c8d;
        }

        .bonus-profit {
            color: #27ae60;
            font-weight: 500;
        }

        .regular-profit {
            color: #3498db;
            font-weight: 500;
        }

        .total-profit {
            color: #f39c12;
            font-weight: bold;
        }

        .payment-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .payment-cash {
            background: #d4edda;
            color: #155724;
        }

        .payment-card {
            background: #d1ecf1;
            color: #0c5460;
        }

        .payment-mixed {
            background: #fff3cd;
            color: #856404;
        }

        .qty-breakdown {
            font-size: 0.8rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: flex;">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <h3>💳 Aanabi Sales</h3>
            <p>Loading enhanced sales system...</p>
        </div>
    </div>

    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                Aanabi Pharmacy v2.0
                <span style="font-size: 0.7rem; opacity: 0.8; margin-left: 0.5rem;">Sales Management</span>
            </div>
            <div class="header-info">
                <span id="current-date"></span> | <span id="current-time"></span>

                <!-- User Info -->
                <div class="user-info" id="user-info">
                    <div class="user-avatar" id="user-avatar">S</div>
                    <div class="user-details">
                        <div class="user-name" id="user-name">Staff Member</div>
                        <div class="user-role" id="user-role">Staff</div>
                    </div>
                    <span class="mode-indicator staff" id="mode-indicator">Staff Mode</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav">
        <div class="nav-content">
            <ul class="nav-menu">
                <li class="nav-item"><a href="../index.html" class="nav-link">🏠 Dashboard</a></li>
                <li class="nav-item"><a href="medicines.html" class="nav-link">💊 Medicines</a></li>
                <li class="nav-item"><a href="inventory.html" class="nav-link">📦 Inventory</a></li>
                <li class="nav-item"><a href="sales.html" class="nav-link active">💳 Sales</a></li>
                <li class="nav-item"><a href="purchase.html" class="nav-link">🛒 Purchase</a></li>
                <li class="nav-item"><a href="customers.html" class="nav-link">👥 Customers</a></li>
                <li class="nav-item"><a href="suppliers.html" class="nav-link">🏭 Suppliers</a></li>
                <li class="nav-item"><a href="reports.html" class="nav-link">📊 Reports</a></li>
                <li class="nav-item"><a href="settings.html" class="nav-link">⚙️ Settings</a></li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">

        <!-- Page Header -->
        <div class="card">
            <div class="card-header">
                <h1 class="card-title">💳 Enhanced Sales & Billing System</h1>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-8">
                        <button class="btn btn-primary" onclick="showNewSaleModal()">➕ New Sale</button>
                        <button class="btn btn-success" onclick="showQuickSaleModal()">⚡ Quick Sale</button>
                        <button class="btn btn-warning admin-only" onclick="exportEnhancedSales()">📤 Export Sales</button>
                        <button class="btn btn-info" onclick="showDailyReport()">📊 Today's Report</button>
                    </div>
                    <div class="col-4">
                        <div class="search-container">
                            <input type="text" id="sales-search" class="search-input" placeholder="Search sales, customers...">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- ✨ ENHANCED: Sales Statistics with Bonus/Regular Breakdown -->
        <div class="enhanced-stats">
            <div class="enhanced-stat-card bonus">
                <div class="stat-header">
                    <span class="stat-title">Bonus Profit</span>
                    <span class="stat-icon">🎁</span>
                </div>
                <div class="enhanced-stat-value" id="today-bonus-profit">Rs. 0</div>
                <div class="stat-subtitle">From free inventory (100% margin)</div>
            </div>

            <div class="enhanced-stat-card regular">
                <div class="stat-header">
                    <span class="stat-title">Regular Profit</span>
                    <span class="stat-icon">📦</span>
                </div>
                <div class="enhanced-stat-value" id="today-regular-profit">Rs. 0</div>
                <div class="stat-subtitle">From paid inventory</div>
            </div>

            <div class="enhanced-stat-card total">
                <div class="stat-header">
                    <span class="stat-title">Total Revenue</span>
                    <span class="stat-icon">💰</span>
                </div>
                <div class="enhanced-stat-value" id="today-total-revenue">Rs. 0</div>
                <div class="stat-subtitle" id="today-transactions">0 transactions</div>
            </div>

            <div class="enhanced-stat-card loyalty">
                <div class="stat-header">
                    <span class="stat-title">Loyalty Points</span>
                    <span class="stat-icon">🏆</span>
                </div>
                <div class="enhanced-stat-value" id="today-loyalty-points">0</div>
                <div class="stat-subtitle">Points redeemed today</div>
            </div>
        </div>

        <!-- ✨ ENHANCED: Sales List with Profit Breakdown -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">📋 Enhanced Sales Transactions</h3>
                <small class="text-muted">Recent sales with detailed profit breakdown and loyalty points</small>
            </div>
            <div class="card-body">
                <div class="table-container">
                    <table class="table sales-table">
                        <thead>
                            <tr>
                                <th>Sale ID</th>
                                <th>Date & Time</th>
                                <th>Customer</th>
                                <th>Medicine</th>
                                <th>Qty (B+R)</th>
                                <th>Amount</th>
                                <th>Profit Breakdown</th>
                                <th>Payment</th>
                                <th>Points</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="enhanced-sales-tbody">
                            <!-- Enhanced sales data will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <!-- ✨ ENHANCED: New Sale Modal with Loyalty Integration -->
    <div id="sale-modal" class="modal-overlay" style="display: none;">
        <div class="modal modal-lg">
            <div class="modal-header">
                <h3 class="modal-title">💳 Enhanced Sale Transaction</h3>
                <button class="modal-close" onclick="closeSaleModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="enhanced-sale-form">
                    <!-- ✨ Enhanced Customer Information -->
                    <div class="section-card">
                        <div class="section-header">
                            <span>👤</span>
                            <h4>Customer Information</h4>
                        </div>
                        <div class="row">
                            <div class="col-4">
                                <div class="form-group customer-search">
                                    <label class="form-label">Customer Phone (Optional)</label>
                                    <input type="text" id="customer-phone" name="customer_phone" class="form-input" 
                                           placeholder="Enter phone number" onkeyup="searchCustomers()" onchange="loadCustomerDetails()">
                                    <div id="customer-suggestions" class="customer-suggestions"></div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="form-group">
                                    <label class="form-label">Customer Name</label>
                                    <input type="text" id="customer-name" name="customer_name" class="form-input" 
                                           placeholder="Walk-in Customer">
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="form-group">
                                    <label class="form-label">🏆 Available Loyalty Points</label>
                                    <input type="text" id="customer-loyalty-points" class="form-input" readonly 
                                           placeholder="0 points" style="background: #fff3e0; font-weight: bold;">
                                </div>
                            </div>
                        </div>
                        
                        <!-- ✨ Enhanced Medical Alert System -->
                        <div id="customer-medical-alert" class="medical-alert" style="display: none;">
                            <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                                <span style="font-size: 1.2rem;">⚠️</span>
                                <strong>Medical Alert - Please Review Before Prescribing</strong>
                            </div>
                            <div id="customer-allergies" class="allergy-warning"></div>
                            <div id="customer-medical-history" style="margin-top: 0.5rem; font-size: 0.9rem; color: #6c757d;"></div>
                        </div>
                    </div>

                    <!-- ✨ Enhanced Medicine Selection with Stock Display -->
                    <div class="section-card">
                        <div class="section-header">
                            <span>💊</span>
                            <h4>Medicine Selection & Stock Information</h4>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="form-label">Medicine *</label>
                                    <select id="medicine-select" name="medicine_id" class="form-select" required onchange="loadEnhancedMedicineDetails()">
                                        <option value="">Select Medicine</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="form-label">Quantity *</label>
                                    <input type="number" id="quantity" name="quantity" class="form-input" 
                                           min="1" required onchange="calculateEnhancedTotal()" style="font-size: 1.2rem; font-weight: bold;">
                                </div>
                            </div>
                        </div>
                        
                        <!-- ✨ Enhanced Stock Information Display -->
                        <div class="stock-allocation" style="display: none;" id="stock-info-section">
                            <div class="row">
                                <div class="col-3">
                                    <div class="points-display" style="background: #e8f5e8; margin: 0;">
                                        <div class="points-label">🎁 Bonus Stock</div>
                                        <div class="points-value" id="bonus-stock-display" style="color: #27ae60;">0</div>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="points-display" style="background: #e3f2fd; margin: 0;">
                                        <div class="points-label">📦 Regular Stock</div>
                                        <div class="points-value" id="regular-stock-display" style="color: #3498db;">0</div>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="points-display" style="margin: 0;">
                                        <div class="points-label">💰 Unit Price</div>
                                        <div class="points-value" id="unit-price-display" style="color: #2c3e50;">Rs. 0</div>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="points-display" style="background: #fff3e0; margin: 0;">
                                        <div class="points-label">🏆 Loyalty Price</div>
                                        <div class="points-value" id="loyalty-price-display" style="color: #f39c12;">0 pts</div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- ✨ Smart Allocation Display -->
                            <div id="allocation-breakdown" style="margin-top: 1rem; padding: 1rem; background: white; border-radius: 8px;">
                                <h5 style="margin: 0 0 1rem 0; color: #2c3e50;">📊 Smart Stock Allocation</h5>
                                <div id="allocation-details">
                                    <!-- Allocation details will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ✨ Enhanced Loyalty Points Redemption -->
                    <div class="loyalty-section" id="loyalty-section" style="display: none;">
                        <div class="loyalty-header">
                            <span style="font-size: 1.5rem;">🏆</span>
                            <h4>Loyalty Points Redemption</h4>
                        </div>
                        <div class="row">
                            <div class="col-4">
                                <div class="points-display">
                                    <div class="points-label">Available Points</div>
                                    <div class="points-value" id="available-points-display">0</div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="form-group">
                                    <label class="form-label">Points to Use</label>
                                    <input type="number" id="loyalty-points-used" name="loyalty_points_used" class="form-input" 
                                           min="0" value="0" onchange="calculateEnhancedTotal()"
                                           style="font-size: 1.1rem; font-weight: bold; text-align: center;">
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="points-display" style="background: #e8f5e8;">
                                    <div class="points-label">Points Value</div>
                                    <div class="points-value" id="points-value-display" style="color: #27ae60;">Rs. 0</div>
                                </div>
                            </div>
                        </div>
                        <div class="alert alert-info" style="margin-top: 1rem; background: rgba(255,255,255,0.8);">
                            <small>
                                <strong>💡 Loyalty System:</strong> 
                                1 point = Rs. 0.10 | Earn 1 point per Rs. 20 spent (after redemption)
                            </small>
                        </div>
                    </div>

                    <!-- ✨ Enhanced Billing Information -->
                    <div class="section-card">
                        <div class="section-header">
                            <span>💳</span>
                            <h4>Billing & Payment Information</h4>
                        </div>
                        <div class="row">
                            <div class="col-3">
                                <div class="form-group">
                                    <label class="form-label">Discount (%)</label>
                                    <input type="number" id="discount-percent" name="discount_percent" 
                                           class="form-input" min="0" max="100" value="0" onchange="calculateEnhancedTotal()">
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="form-group">
                                    <label class="form-label">Discount Amount</label>
                                    <input type="text" id="discount-amount" class="form-input" readonly 
                                           style="background: #f8f9fa;">
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="form-group">
                                    <label class="form-label">Tax Amount</label>
                                    <input type="text" id="tax-amount" class="form-input" readonly 
                                           style="background: #f8f9fa;">
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="form-group">
                                    <label class="form-label">Tax System</label>
                                    <input type="text" id="tax-system-display" class="form-input" readonly 
                                           style="background: #e8f5e8; color: #27ae60;">
                                </div>
                            </div>
                        </div>
                        
                        <!-- Enhanced Payment Method Selection -->
                        <div class="form-group">
                            <label class="form-label">Payment Method *</label>
                            <div class="payment-methods">
                                <div class="payment-option" onclick="selectPaymentMethod('Cash')">
                                    <div class="payment-icon">💵</div>
                                    <div>Cash</div>
                                </div>
                                <div class="payment-option" onclick="selectPaymentMethod('Card')">
                                    <div class="payment-icon">💳</div>
                                    <div>Card</div>
                                </div>
                                <div class="payment-option" onclick="selectPaymentMethod('Digital Payment')">
                                    <div class="payment-icon">📱</div>
                                    <div>Digital</div>
                                </div>
                                <div class="payment-option" onclick="selectPaymentMethod('Mixed')">
                                    <div class="payment-icon">🔄</div>
                                    <div>Mixed</div>
                                </div>
                            </div>
                            <input type="hidden" id="payment-method" name="payment_method" required>
                        </div>
                        
                        <!-- ✨ Enhanced Total Display -->
                        <div class="row" style="margin-top: 2rem; padding: 1rem; background: linear-gradient(135deg, #f8f9fa, #e9ecef); border-radius: 8px;">
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="form-label" style="font-size: 1.2rem; font-weight: bold; color: #2c3e50;">💰 Cash Amount</label>
                                    <input type="text" id="cash-amount" class="form-input" readonly 
                                           style="font-size: 1.3rem; font-weight: bold; color: #27ae60; background: white;">
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="form-label" style="font-size: 1.2rem; font-weight: bold; color: #2c3e50;">📈 Total Profit</label>
                                    <input type="text" id="total-profit" class="form-input" readonly 
                                           style="font-size: 1.3rem; font-weight: bold; color: #f39c12; background: white;">
                                </div>
                            </div>
                        </div>
                        
                        <div style="text-align: center; margin-top: 1rem;">
                            <div style="font-size: 1.8rem; font-weight: bold; color: #2c3e50; margin-bottom: 0.5rem;">
                                Final Total: <span id="final-total-display">Rs. 0</span>
                            </div>
                            <div style="font-size: 0.9rem; color: #7f8c8d;" id="payment-breakdown">
                                <!-- Payment breakdown will be shown here -->
                            </div>
                        </div>

                        <div class="form-group" style="margin-top: 2rem; text-align: center;">
                            <button type="submit" class="btn btn-primary btn-lg" style="padding: 1rem 2rem; font-size: 1.1rem;">
                                💳 Complete Enhanced Sale
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="closeSaleModal()" style="margin-left: 1rem;">
                                Cancel
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Quick Sale Modal (Enhanced) -->
    <div id="quick-sale-modal" class="modal-overlay" style="display: none;">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">⚡ Enhanced Quick Sale</h3>
                <button class="modal-close" onclick="closeQuickSaleModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="quick-sale-form">
                    <div class="form-group">
                        <label class="form-label">Medicine *</label>
                        <select id="quick-medicine-select" name="medicine_id" class="form-select" required onchange="loadQuickMedicineDetails()">
                            <option value="">Select Medicine</option>
                        </select>
                    </div>
                    
                    <div class="row">
                        <div class="col-4">
                            <div class="form-group">
                                <label class="form-label">🎁 Bonus Stock</label>
                                <input type="text" id="quick-bonus-stock" class="form-input" readonly style="background: #e8f5e8;">
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="form-group">
                                <label class="form-label">📦 Regular Stock</label>
                                <input type="text" id="quick-regular-stock" class="form-input" readonly style="background: #e3f2fd;">
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="form-group">
                                <label class="form-label">💰 Unit Price</label>
                                <input type="text" id="quick-unit-price" class="form-input" readonly>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label">Quantity *</label>
                                <input type="number" id="quick-quantity" name="quantity" class="form-input" 
                                       min="1" required onchange="calculateQuickTotal()" style="font-size: 1.2rem; font-weight: bold;">
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label" style="font-weight: bold;">Total Amount</label>
                                <input type="text" id="quick-total" class="form-input" readonly 
                                       style="font-weight: bold; color: #2c3e50; font-size: 1.2rem;">
                            </div>
                        </div>
                    </div>

                    <!-- Quick Payment Methods -->
                    <div class="form-group">
                        <label class="form-label">Payment Method *</label>
                        <div class="payment-methods">
                            <div class="payment-option selected" onclick="selectQuickPaymentMethod('Cash')">
                                <div class="payment-icon">💵</div>
                                <div>Cash</div>
                            </div>
                            <div class="payment-option" onclick="selectQuickPaymentMethod('Card')">
                                <div class="payment-icon">💳</div>
                                <div>Card</div>
                            </div>
                            <div class="payment-option" onclick="selectQuickPaymentMethod('Digital Payment')">
                                <div class="payment-icon">📱</div>
                                <div>Digital</div>
                            </div>
                        </div>
                        <input type="hidden" id="quick-payment-method" name="payment_method" value="Cash" required>
                    </div>

                    <div class="form-group" style="text-align: center; margin-top: 2rem;">
                        <button type="submit" class="btn btn-primary btn-lg">⚡ Complete Quick Sale</button>
                        <button type="button" class="btn btn-secondary" onclick="closeQuickSaleModal()">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Enhanced Bill Print Modal -->
    <div id="bill-modal" class="modal-overlay" style="display: none;">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">🧾 Enhanced Sale Receipt</h3>
                <button class="modal-close" onclick="closeBillModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="bill-content" id="bill-content">
                    <!-- Enhanced bill content will be generated here -->
                </div>
            </div>
            <div style="padding: 1rem; border-top: 1px solid #e0e0e0; text-align: center;">
                <button class="btn btn-primary" onclick="printEnhancedBill()">🖨️ Print Receipt</button>
                <button class="btn btn-success" onclick="downloadBillPDF()">📄 Download PDF</button>
                <button class="btn btn-secondary" onclick="closeBillModal()">Close</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/database.js"></script>
    <script src="../js/auth.js"></script>
    <script src="../js/audit.js"></script>
    <script src="../js/notifications.js"></script>
    <script src="../js/sales.js"></script>
    
    <script>
        // ✨ Enhanced Sales Page Initialization
        async function initializeEnhancedSales() {
            try {
                console.log('💳 Enhanced sales page initialization started...');
                
                // Wait for systems to be ready
                await waitForSystems();
                
                // Check authentication
                if (!authManager.isLoggedIn()) {
                    authManager.redirectToLogin('Please login to access sales');
                    return;
                }
                
                // Setup user interface
                setupUserInterface();
                
                // Initialize enhanced sales system
                await initializeEnhancedSalesSystem();
                
                // Hide loading overlay
                document.getElementById('loading-overlay').style.display = 'none';
                
                console.log('✅ Enhanced sales system initialized successfully');
                
            } catch (error) {
                console.error('❌ Sales initialization failed:', error);
                showError('Failed to initialize sales system');
            }
        }

        async function waitForSystems() {
            let attempts = 0;
            const maxAttempts = 100;

            while ((!dbManager?.isReady() || !authManager) && attempts < maxAttempts) {
                await new Promise(resolve => setTimeout(resolve, 100));
                attempts++;
            }

            if (!dbManager?.isReady() || !authManager) {
                throw new Error('Systems failed to initialize');
            }
        }

        function setupUserInterface() {
            const user = authManager.getCurrentUser();
            if (!user) return;

            // Update user info display
            document.getElementById('user-avatar').textContent = user.full_name.charAt(0).toUpperCase();
            document.getElementById('user-name').textContent = user.full_name;
            document.getElementById('user-role').textContent = user.role;
            document.getElementById('user-info').style.display = 'flex';

            // Set role-based styling
            document.body.className = user.role;

            console.log(`💳 Enhanced sales loaded for ${user.full_name} (${user.role})`);
        }

        async function initializeEnhancedSalesSystem() {
            // Load enhanced statistics
            await loadEnhancedSalesStatistics();
            
            // Load sales table
            await loadEnhancedSalesTable();
            
            // Load medicine options
            await loadMedicineOptions();
            
            // Setup enhanced event listeners
            setupEnhancedEventListeners();
            
            // Load system settings for tax calculation
            await loadSystemSettings();
        }

        async function loadEnhancedSalesStatistics() {
            try {
                const today = new Date().toISOString().split('T')[0];
                const sales = await dbManager.getAll('sales');
                const todaySales = sales.filter(sale => sale.date === today);
                
                let bonusProfit = 0;
                let regularProfit = 0;
                let totalRevenue = 0;
                let loyaltyPointsRedeemed = 0;
                
                todaySales.forEach(sale => {
                    bonusProfit += parseFloat(sale.bonus_profit || 0);
                    regularProfit += parseFloat(sale.regular_profit || 0);
                    totalRevenue += parseFloat(sale.final_total || sale.total_amount || 0);
                    loyaltyPointsRedeemed += parseInt(sale.loyalty_points_used || 0);
                });
                
                // Update enhanced statistics
                document.getElementById('today-bonus-profit').textContent = `Rs. ${bonusProfit.toLocaleString()}`;
                document.getElementById('today-regular-profit').textContent = `Rs. ${regularProfit.toLocaleString()}`;
                document.getElementById('today-total-revenue').textContent = `Rs. ${totalRevenue.toLocaleString()}`;
                document.getElementById('today-transactions').textContent = `${todaySales.length} transactions`;
                document.getElementById('today-loyalty-points').textContent = loyaltyPointsRedeemed.toLocaleString();
                
                console.log(`📊 Today's enhanced stats: Revenue Rs.${totalRevenue}, Bonus Rs.${bonusProfit}, Regular Rs.${regularProfit}`);
                
            } catch (error) {
                console.error('Error loading enhanced sales statistics:', error);
            }
        }

        async function loadEnhancedSalesTable() {
            try {
                const sales = await dbManager.getAll('sales');
                const tbody = document.getElementById('enhanced-sales-tbody');
                
                if (!tbody) return;
                
                tbody.innerHTML = '';
                
                // Sort by date and time (newest first)
                sales.sort((a, b) => new Date(`${b.date} ${b.time}`) - new Date(`${a.date} ${a.time}`));
                
                // Show latest 50 sales
                sales.slice(0, 50).forEach(sale => {
                    const row = document.createElement('tr');
                    
                    // Enhanced quantity display with bonus/regular breakdown
                    const qtyBreakdown = sale.bonus_qty_sold || sale.regular_qty_sold ? 
                        `<div class="qty-breakdown">${sale.bonus_qty_sold || 0}B + ${sale.regular_qty_sold || 0}R</div>` : '';
                    
                    // Enhanced profit breakdown
                    const profitBreakdown = `
                        <div class="profit-breakdown">
                            <div class="bonus-profit">Bonus: Rs. ${(sale.bonus_profit || 0).toLocaleString()}</div>
                            <div class="regular-profit">Regular: Rs. ${(sale.regular_profit || 0).toLocaleString()}</div>
                            <div class="total-profit">Total: Rs. ${(sale.total_profit || sale.profit_amount || 0).toLocaleString()}</div>
                        </div>
                    `;
                    
                    // Payment method with points indicator
                    const paymentDisplay = sale.loyalty_points_used && sale.loyalty_points_used > 0 ? 
                        `<span class="payment-badge payment-mixed">${sale.payment_method}</span><br><small>+${sale.loyalty_points_used} pts</small>` :
                        `<span class="payment-badge payment-${(sale.payment_method || 'cash').toLowerCase()}">${sale.payment_method}</span>`;
                    
                    row.innerHTML = `
                        <td><strong>${sale.sale_id}</strong></td>
                        <td>
                            ${new Date(sale.date).toLocaleDateString()}<br>
                            <small>${sale.time}</small>
                        </td>
                        <td>
                            ${sale.customer_name}<br>
                            <small class="text-muted">${sale.customer_phone || 'N/A'}</small>
                        </td>
                        <td>${sale.medicine_name}</td>
                        <td>
                            <strong>${sale.quantity}</strong>
                            ${qtyBreakdown}
                        </td>
                        <td><strong>Rs. ${(sale.final_total || sale.total_amount || 0).toLocaleString()}</strong></td>
                        <td>${profitBreakdown}</td>
                        <td>${paymentDisplay}</td>
                        <td>
                            ${sale.loyalty_points_used ? `<span style="color: #e74c3c;">-${sale.loyalty_points_used}</span><br>` : ''}
                            ${sale.loyalty_points_earned ? `<span style="color: #27ae60;">+${sale.loyalty_points_earned}</span>` : ''}
                        </td>
                        <td>
                            <button class="btn btn-sm btn-primary" onclick="viewEnhancedBill('${sale.sale_id}')">🧾</button>
                            <button class="btn btn-sm btn-success" onclick="duplicateSale('${sale.sale_id}')">🔄</button>
                        </td>
                    `;
                    tbody.appendChild(row);
                });
                
            } catch (error) {
                console.error('Error loading enhanced sales table:', error);
            }
        }

        async function loadMedicineOptions() {
            try {
                const medicines = await dbManager.getAll('medicines');
                const activeMedicines = medicines.filter(med => med.status === 'Active');
                
                // Populate both regular and quick sale selects
                const selects = ['medicine-select', 'quick-medicine-select'];
                
                selects.forEach(selectId => {
                    const select = document.getElementById(selectId);
                    if (select) {
                        select.innerHTML = '<option value="">Select Medicine</option>';
                        activeMedicines.forEach(medicine => {
                            const totalStock = (medicine.regular_stock || 0) + (medicine.bonus_stock || 0);
                            const option = document.createElement('option');
                            option.value = medicine.medicine_id;
                            option.textContent = `${medicine.medicine_name} (Stock: ${totalStock})`;
                            option.dataset.bonusStock = medicine.bonus_stock || 0;
                            option.dataset.regularStock = medicine.regular_stock || 0;
                            option.dataset.sellingPrice = medicine.selling_price || 0;
                            option.dataset.loyaltyPointPrice = medicine.loyalty_point_price || 0;
                            select.appendChild(option);
                        });
                    }
                });
                
            } catch (error) {
                console.error('Error loading medicine options:', error);
            }
        }

        function setupEnhancedEventListeners() {
            // Enhanced sale form
            const saleForm = document.getElementById('enhanced-sale-form');
            if (saleForm) {
                saleForm.addEventListener('submit', handleEnhancedSaleSubmit);
            }
            
            // Quick sale form
            const quickSaleForm = document.getElementById('quick-sale-form');
            if (quickSaleForm) {
                quickSaleForm.addEventListener('submit', handleQuickSaleSubmit);
            }
            
            // Search functionality
            const searchInput = document.getElementById('sales-search');
            if (searchInput) {
                searchInput.addEventListener('input', handleSalesSearch);
            }
        }

        // Enhanced form handlers and UI functions will continue...
        // This is a comprehensive enhancement with all Phase 3 features integrated

        // Enhanced sale submit handler
        async function handleEnhancedSaleSubmit(event) {
            if (window.salesManager) {
                return await window.salesManager.handleSaleSubmit(event);
            } else {
                console.error('Sales manager not available');
                event.preventDefault();
            }
        }

        // Quick sale submit handler
        async function handleQuickSaleSubmit(event) {
            if (window.salesManager) {
                return await window.salesManager.handleQuickSaleSubmit(event);
            } else {
                console.error('Sales manager not available');
                event.preventDefault();
            }
        }

        // Sales search handler
        function handleSalesSearch(event) {
            if (window.salesManager) {
                return window.salesManager.filterSales(event.target.value);
            }
        }

        // Load system settings
        async function loadSystemSettings() {
            if (window.salesManager) {
                return await window.salesManager.loadSettings();
            } else {
                console.error('Sales manager not available for loading settings');
            }
        }

        // Mode switching functionality is now handled by the auth system
        // No logout functionality - users switch between Staff and Admin modes

        function showError(message) {
            alert('Error: ' + message);
        }

        // Placeholder functions for enhanced features
        function showNewSaleModal() {
            document.getElementById('sale-modal').style.display = 'flex';
        }

        function closeSaleModal() {
            document.getElementById('sale-modal').style.display = 'none';
        }

        function showQuickSaleModal() {
            document.getElementById('quick-sale-modal').style.display = 'flex';
        }

        function closeQuickSaleModal() {
            document.getElementById('quick-sale-modal').style.display = 'none';
        }

        function showDailyReport() {
            alert('Enhanced daily report feature ready for implementation! 📊');
        }

        function exportEnhancedSales() {
            alert('Enhanced sales export feature ready for implementation! 📤');
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initializeEnhancedSales);
    </script>
</body>
</html>