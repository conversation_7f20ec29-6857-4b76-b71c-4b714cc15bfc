<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - Aanabi Pharmacy v2.0</title>
    <link rel="stylesheet" href="../css/style.css">
    <style>
        /* Enhanced Settings Styles */
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
            color: white;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .user-details {
            display: flex;
            flex-direction: column;
            font-size: 0.9rem;
        }

        .user-name {
            font-weight: 600;
        }

        .user-role {
            opacity: 0.8;
            font-size: 0.8rem;
            text-transform: capitalize;
        }

        /* Mode Switch Section */
        .mode-switch-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 2px solid #dee2e6;
            text-align: center;
        }

        .current-mode {
            font-size: 1.2rem;
            margin-bottom: 1rem;
        }

        .mode-badge {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin: 0.5rem;
        }

        .mode-badge.staff {
            background: #3498db;
            color: white;
        }

        .mode-badge.admin {
            background: #e74c3c;
            color: white;
        }

        .mode-switch-btn {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 1rem;
        }

        .mode-switch-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(39, 174, 96, 0.3);
        }

        .mode-switch-btn.admin-to-staff {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .mode-switch-btn.admin-to-staff:hover {
            box-shadow: 0 8px 25px rgba(243, 156, 18, 0.3);
        }

        .mode-description {
            margin-top: 1rem;
            color: #6c757d;
            font-size: 0.9rem;
            line-height: 1.6;
        }

        /* Permission-based hiding */
        .admin-only {
            display: none;
        }

        body.admin .admin-only {
            display: block;
        }

        body.admin .admin-only.inline {
            display: inline-block;
        }

        body.admin .admin-only.flex {
            display: flex;
        }

        .staff-only {
            display: block;
        }

        body.admin .staff-only {
            display: none;
        }

        /* Tax System Toggle */
        .tax-system-selector {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            border: 2px solid #e9ecef;
        }

        .tax-system-selector.active {
            border-color: #3498db;
            background: #e3f2fd;
        }

        .tax-option {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
            cursor: pointer;
        }

        .tax-option input[type="radio"] {
            margin-right: 0.5rem;
        }

        .tax-system-status {
            text-align: center;
            margin-bottom: 1rem;
        }

        .badge {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .badge-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .badge-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .tax-fields-section {
            transition: all 0.3s ease;
        }

        .hidden-field {
            display: none;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                🏥 Aanabi Pharmacy v2.0
                <span style="font-size: 0.7rem; opacity: 0.8; margin-left: 0.5rem;">Simplified Edition</span>
            </div>
            <div class="header-info">
                <span id="current-date"></span> | <span id="current-time"></span>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav">
        <div class="nav-content">
            <ul class="nav-menu">
                <li class="nav-item"><a href="../index.html" class="nav-link">🏠 Dashboard</a></li>
                <li class="nav-item"><a href="medicines.html" class="nav-link">💊 Medicines</a></li>
                <li class="nav-item"><a href="inventory.html" class="nav-link">📦 Inventory</a></li>
                <li class="nav-item"><a href="sales.html" class="nav-link">💳 Sales</a></li>
                <li class="nav-item"><a href="purchase.html" class="nav-link">🛒 Purchase</a></li>
                <li class="nav-item"><a href="customers.html" class="nav-link">👥 Customers</a></li>
                <li class="nav-item"><a href="suppliers.html" class="nav-link">🏭 Suppliers</a></li>
                <li class="nav-item"><a href="reports.html" class="nav-link">📊 Reports</a></li>
                <li class="nav-item"><a href="settings.html" class="nav-link active">⚙️ Settings</a></li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">

        <!-- Page Header -->
        <div class="card">
            <div class="card-header">
                <h1 class="card-title">⚙️ System Settings v2.0</h1>
            </div>
            <div class="card-body">
                <p class="text-muted">Configure your pharmacy management system</p>
                
                <!-- Basic Statistics -->
                <div class="dashboard-stats">
                    <div class="stat-card">
                        <span class="stat-icon">💊</span>
                        <div class="stat-value" id="total-medicines">0</div>
                        <div class="stat-label">Total Medicines</div>
                    </div>
                    <div class="stat-card">
                        <span class="stat-icon">👥</span>
                        <div class="stat-value" id="total-customers">0</div>
                        <div class="stat-label">Total Customers</div>
                    </div>
                    <div class="stat-card">
                        <span class="stat-icon">💳</span>
                        <div class="stat-value" id="total-sales">0</div>
                        <div class="stat-label">Total Sales</div>
                    </div>
                    <div class="stat-card admin-only">
                        <span class="stat-icon">🏭</span>
                        <div class="stat-value" id="total-suppliers">0</div>
                        <div class="stat-label">Total Suppliers</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- ✨ ENHANCED: Tax System Configuration (Admin Only) -->
            <div class="col-6 admin-only">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">🧾 Tax System Configuration</h3>
                    </div>
                    <div class="card-body">
                        <!-- Tax System Status -->
                        <div class="tax-system-status" id="tax-system-status">
                            <span class="badge badge-success">
                                📊 VAT System Active (13% Tax)
                            </span>
                        </div>

                        <form id="tax-system-form">
                            <div class="form-group">
                                <label class="form-label">Select Tax System *</label>
                                <div class="tax-system-selector">
                                    <div class="tax-option">
                                        <input type="radio" id="tax-vat" name="tax_system" value="vat" checked>
                                        <label for="tax-vat">
                                            <strong>📊 VAT System (13% Tax)</strong><br>
                                            <small class="text-muted">Value Added Tax - Standard 13% on all transactions</small>
                                        </label>
                                    </div>
                                    <div class="tax-option">
                                        <input type="radio" id="tax-pan" name="tax_system" value="pan">
                                        <label for="tax-pan">
                                            <strong>📄 PAN System (0% Tax)</strong><br>
                                            <small class="text-muted">Permanent Account Number - No additional tax</small>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- VAT Fields -->
                            <div id="vat-fields" class="tax-fields-section">
                                <div class="form-group">
                                    <label class="form-label">VAT Rate (%)</label>
                                    <input type="number" id="vat-rate" name="vat_rate" class="form-input" 
                                           min="0" max="100" step="0.01" value="13">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">VAT Registration Number</label>
                                    <input type="text" id="vat-registration-number" name="vat_registration_number" 
                                           class="form-input" placeholder="VAT-*********" value="VAT-*********">
                                </div>
                            </div>

                            <!-- PAN Fields -->
                            <div id="pan-fields" class="tax-fields-section hidden-field">
                                <div class="form-group">
                                    <label class="form-label">PAN Number</label>
                                    <input type="text" id="pan-number" name="pan_number" 
                                           class="form-input" placeholder="PAN-*********" value="PAN-*********">
                                </div>
                                <div class="alert alert-info">
                                    <strong>ℹ️ Note:</strong> PAN system applies 0% additional tax on transactions.
                                </div>
                            </div>

                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">💾 Save Tax System</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- ✨ ENHANCED: Loyalty Points & Business Rules -->
            <div class="col-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">🏆 Loyalty Points System</h3>
                    </div>
                    <div class="card-body">
                        <form id="loyalty-system-form">
                            <div class="form-group">
                                <label class="form-label">Points Earning Rate</label>
                                <div class="form-row">
                                    <div class="form-group">
                                        <input type="number" id="loyalty-points-rate" name="loyalty_points_rate" 
                                               class="form-input" min="1" value="20">
                                    </div>
                                    <div class="form-group" style="flex: 2;">
                                        <span class="form-input" style="background: #f8f9fa; color: #6c757d;">
                                            Rs. per point earned
                                        </span>
                                    </div>
                                </div>
                                <small class="text-muted">Customer earns 1 point for every Rs. spent</small>
                            </div>

                            <div class="form-group">
                                <label class="form-label">Point Redemption Value</label>
                                <div class="form-row">
                                    <div class="form-group">
                                        <span class="form-input" style="background: #f8f9fa; color: #6c757d;">
                                            1 point = Rs.
                                        </span>
                                    </div>
                                    <div class="form-group">
                                        <input type="number" id="loyalty-redemption-rate" name="loyalty_redemption_rate" 
                                               class="form-input" min="0" step="0.01" value="0.10">
                                    </div>
                                </div>
                                <small class="text-muted">Monetary value when redeeming points</small>
                            </div>

                            <div class="form-group">
                                <label class="form-label">Default Profit Margin (%)</label>
                                <input type="number" id="default-margin" name="default_margin" 
                                       class="form-input" min="0" max="1000" step="0.01" value="60">
                                <small class="text-muted">Default margin for regular stock (bonus margin is always 100%)</small>
                            </div>

                            <div class="form-group">
                                <button type="submit" class="btn btn-success">🏆 Save Loyalty Settings</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pharmacy Information -->
        <div class="row">
            <div class="col-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">🏥 Pharmacy Information</h3>
                    </div>
                    <div class="card-body">
                        <form id="pharmacy-info-form">
                            <div class="form-group">
                                <label class="form-label">Pharmacy Name *</label>
                                <input type="text" id="pharmacy-name" name="pharmacy_name" class="form-input" 
                                       value="Aanabi Pharmacy" required>
                            </div>

                            <div class="form-group">
                                <label class="form-label">License Number</label>
                                <input type="text" id="license-number" name="license_number" class="form-input"
                                       value="PH-2024-001">
                            </div>

                            <div class="form-group">
                                <label class="form-label">Address</label>
                                <textarea id="address" name="address" class="form-textarea" rows="3">Main Street, Kathmandu, Nepal</textarea>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">Phone Number</label>
                                    <input type="tel" id="phone" name="phone" class="form-input" value="01-4567890">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Email</label>
                                    <input type="email" id="email" name="email" class="form-input" 
                                           value="<EMAIL>">
                                </div>
                            </div>

                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">💾 Save Pharmacy Info</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Alert Settings -->
            <div class="col-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">🔔 Alert Settings</h3>
                    </div>
                    <div class="card-body">
                        <form id="alert-settings-form">
                            <div class="form-group">
                                <label class="form-label">Low Stock Alert Threshold (Days)</label>
                                <input type="number" id="low-stock-days" name="low_stock_alert_days" 
                                       class="form-input" min="1" max="365" value="30">
                                <small class="text-muted">Alert when stock will last for fewer days</small>
                            </div>

                            <div class="form-group">
                                <label class="form-label">Expiry Alert Threshold (Days)</label>
                                <input type="number" id="expiry-alert-days" name="expiry_alert_days" 
                                       class="form-input" min="1" max="365" value="60">
                                <small class="text-muted">Alert when medicines expire within days</small>
                            </div>

                            <div class="form-group admin-only">
                                <label class="form-label">Payment Due Alert (Days)</label>
                                <input type="number" id="payment-due-days" name="payment_due_alert_days" 
                                       class="form-input" min="1" max="365" value="7">
                                <small class="text-muted">Alert for payments due within days</small>
                            </div>

                            <div class="form-group">
                                <button type="submit" class="btn btn-warning">🔔 Save Alert Settings</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Information -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">ℹ️ System Information</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-3">
                        <strong>System Version:</strong><br>
                        <span class="text-muted">v2.0 Simplified Edition</span>
                    </div>
                    <div class="col-3">
                        <strong>Database:</strong><br>
                        <span class="text-muted">IndexedDB (Browser Storage)</span>
                    </div>
                    <div class="col-3">
                        <strong>Authentication:</strong><br>
                        <span class="text-muted">Simplified Mode System</span>
                    </div>
                    <div class="col-3">
                        <strong>Current Mode:</strong><br>
                        <span class="text-muted" id="system-current-mode">Staff Mode</span>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="alert alert-info">
                            <strong>💡 About Simplified Authentication:</strong><br>
                            This system starts in Staff mode by default. Use the mode switch above to access Admin features when needed.
                            Admin password: <code>admin123</code>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Management (Admin Only) -->
        <div class="card admin-only">
            <div class="card-header">
                <h3 class="card-title">💾 Data Management</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <h4>📤 Export Data</h4>
                                <p class="text-muted">Export system data for backup</p>
                                <button class="btn btn-success" onclick="exportData()">📤 Export All Data</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <h4>💾 Backup System</h4>
                                <p class="text-muted">Create complete backup</p>
                                <button class="btn btn-warning" onclick="createBackup()">💾 Create Backup</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <h4>🧹 Cleanup Data</h4>
                                <p class="text-muted">Clean old logs and optimize</p>
                                <button class="btn btn-secondary" onclick="cleanupData()">🧹 Cleanup</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div>&copy; 2025 Aanabi Pharmacy Management System v2.0 - Simplified Edition</div>
    </footer>

    <!-- Scripts -->
    <script src="../js/database.js"></script>
    <script src="../js/auth.js"></script>
    <script src="../js/notifications.js"></script>
    <script src="../js/audit.js"></script>
    
    <script>
        async function initializeSettings() {
            try {
                console.log('⚙️ Settings page initialization started...');
                
                // Wait for systems to be ready
                await waitForSystems();
                
                // No authentication needed
                
                // Setup user interface
                setupUserInterface();
                
                // Load basic statistics
                await loadBasicStatistics();
                
                // Setup event listeners
                setupEventListeners();
                
                console.log('✅ Simplified settings initialized successfully');
                
            } catch (error) {
                console.error('❌ Settings initialization failed:', error);
                showError('Failed to initialize settings page: ' + error.message);
            }
        }

        async function waitForSystems() {
            let attempts = 0;
            const maxAttempts = 100;

            while (!dbManager?.isReady() && attempts < maxAttempts) {
                await new Promise(resolve => setTimeout(resolve, 100));
                attempts++;
                if (attempts % 20 === 0) {
                    console.log(`⏳ Waiting for systems... (${attempts/10}s)`);
                }
            }

            if (!dbManager?.isReady()) {
                throw new Error('Database failed to initialize within timeout');
            }
        }

        function setupUserInterface() {
            // No user interface setup needed - no authentication
            console.log('⚙️ Settings loaded - no authentication required');
        }

        async function loadBasicStatistics() {
            try {
                const [medicines, customers, sales, suppliers] = await Promise.all([
                    dbManager.getAll('medicines'),
                    dbManager.getAll('customers'),
                    dbManager.getAll('sales'),
                    dbManager.getAll('suppliers')
                ]);

                document.getElementById('total-medicines').textContent = medicines.length;
                document.getElementById('total-customers').textContent = customers.length;
                document.getElementById('total-sales').textContent = sales.length;
                
                const suppliersElement = document.getElementById('total-suppliers');
                if (suppliersElement) {
                    suppliersElement.textContent = suppliers.length;
                }

            } catch (error) {
                console.error('Error loading statistics:', error);
            }
        }

        function setupEventListeners() {
            // Update date and time
            updateDateTime();
            setInterval(updateDateTime, 1000);

            // Tax system form
            const taxSystemForm = document.getElementById('tax-system-form');
            if (taxSystemForm) {
                taxSystemForm.addEventListener('submit', handleTaxSystemSubmit);
            }

            // Loyalty system form
            const loyaltySystemForm = document.getElementById('loyalty-system-form');
            if (loyaltySystemForm) {
                loyaltySystemForm.addEventListener('submit', handleLoyaltySystemSubmit);
            }

            // Pharmacy info form
            const pharmacyInfoForm = document.getElementById('pharmacy-info-form');
            if (pharmacyInfoForm) {
                pharmacyInfoForm.addEventListener('submit', handlePharmacyInfoSubmit);
            }

            // Alert settings form
            const alertSettingsForm = document.getElementById('alert-settings-form');
            if (alertSettingsForm) {
                alertSettingsForm.addEventListener('submit', handleAlertSettingsSubmit);
            }

            // Tax system radio buttons
            const taxSystemRadios = document.querySelectorAll('input[name="tax_system"]');
            taxSystemRadios.forEach(radio => {
                radio.addEventListener('change', handleTaxSystemChange);
            });
        }

        function updateDateTime() {
            const now = new Date();
            const dateOptions = { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
            };
            const timeOptions = { 
                hour: '2-digit', 
                minute: '2-digit', 
                second: '2-digit' 
            };

            const dateElement = document.getElementById('current-date');
            const timeElement = document.getElementById('current-time');
            
            if (dateElement && timeElement) {
                dateElement.textContent = now.toLocaleDateString('en-US', dateOptions);
                timeElement.textContent = now.toLocaleTimeString('en-US', timeOptions);
            }
        }

        // Mode switching functions removed - no authentication needed

        // Form submission handlers
        async function handleTaxSystemSubmit(event) {
            event.preventDefault();
            
            try {
                const formData = new FormData(event.target);
                const settings = Object.fromEntries(formData.entries());
                
                // Save settings to database
                for (const [key, value] of Object.entries(settings)) {
                    const setting = { key: key, value: value };
                    try {
                        const existing = await dbManager.get('settings', key);
                        if (existing) {
                            await dbManager.update('settings', setting);
                        } else {
                            await dbManager.insert('settings', setting);
                        }
                    } catch (error) {
                        await dbManager.insert('settings', setting);
                    }
                }
                
                updateTaxSystemDisplay(settings.tax_system);
                showAlert('Tax system configuration updated successfully! 📊', 'success');
                
            } catch (error) {
                console.error('Error saving tax system:', error);
                showAlert('Error saving tax system configuration', 'error');
            }
        }

        async function handleLoyaltySystemSubmit(event) {
            event.preventDefault();
            
            try {
                const formData = new FormData(event.target);
                const settings = Object.fromEntries(formData.entries());
                
                // Save settings to database
                for (const [key, value] of Object.entries(settings)) {
                    const setting = { key: key, value: value };
                    try {
                        const existing = await dbManager.get('settings', key);
                        if (existing) {
                            await dbManager.update('settings', setting);
                        } else {
                            await dbManager.insert('settings', setting);
                        }
                    } catch (error) {
                        await dbManager.insert('settings', setting);
                    }
                }
                
                showAlert('Loyalty system configuration updated successfully! 🏆', 'success');
                
            } catch (error) {
                console.error('Error saving loyalty system:', error);
                showAlert('Error saving loyalty system configuration', 'error');
            }
        }

        async function handlePharmacyInfoSubmit(event) {
            event.preventDefault();
            
            try {
                const formData = new FormData(event.target);
                const settings = Object.fromEntries(formData.entries());
                
                // Save settings to database
                for (const [key, value] of Object.entries(settings)) {
                    const setting = { key: key, value: value };
                    try {
                        const existing = await dbManager.get('settings', key);
                        if (existing) {
                            await dbManager.update('settings', setting);
                        } else {
                            await dbManager.insert('settings', setting);
                        }
                    } catch (error) {
                        await dbManager.insert('settings', setting);
                    }
                }
                
                showAlert('Pharmacy information updated successfully! 🏥', 'success');
                
            } catch (error) {
                console.error('Error saving pharmacy info:', error);
                showAlert('Error saving pharmacy information', 'error');
            }
        }

        async function handleAlertSettingsSubmit(event) {
            event.preventDefault();
            
            try {
                const formData = new FormData(event.target);
                const settings = Object.fromEntries(formData.entries());
                
                // Save settings to database
                for (const [key, value] of Object.entries(settings)) {
                    const setting = { key: key, value: value };
                    try {
                        const existing = await dbManager.get('settings', key);
                        if (existing) {
                            await dbManager.update('settings', setting);
                        } else {
                            await dbManager.insert('settings', setting);
                        }
                    } catch (error) {
                        await dbManager.insert('settings', setting);
                    }
                }
                
                showAlert('Alert settings updated successfully! 🔔', 'success');
                
            } catch (error) {
                console.error('Error saving alert settings:', error);
                showAlert('Error saving alert settings', 'error');
            }
        }

        function handleTaxSystemChange(event) {
            updateTaxSystemDisplay(event.target.value);
        }

        function updateTaxSystemDisplay(taxSystem) {
            const vatFields = document.getElementById('vat-fields');
            const panFields = document.getElementById('pan-fields');
            const taxSystemStatus = document.getElementById('tax-system-status');
            
            if (taxSystem === 'vat') {
                vatFields.classList.remove('hidden-field');
                panFields.classList.add('hidden-field');
                taxSystemStatus.innerHTML = `
                    <span class="badge badge-success">
                        📊 VAT System Active (13% Tax)
                    </span>
                `;
            } else {
                vatFields.classList.add('hidden-field');
                panFields.classList.remove('hidden-field');
                taxSystemStatus.innerHTML = `
                    <span class="badge badge-info">
                        📄 PAN System Active (0% Tax)
                    </span>
                `;
            }
        }

        // Data management functions
        async function exportData() {
            
            try {
                const [medicines, customers, sales, suppliers, settings] = await Promise.all([
                    dbManager.getAll('medicines'),
                    dbManager.getAll('customers'),
                    dbManager.getAll('sales'),
                    dbManager.getAll('suppliers'),
                    dbManager.getAll('settings')
                ]);

                const exportData = {
                    exportDate: new Date().toISOString(),
                    version: '2.0',
                    medicines,
                    customers,
                    sales,
                    suppliers,
                    settings
                };

                const dataStr = JSON.stringify(exportData, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });
                
                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `aanabi_backup_${new Date().toISOString().split('T')[0]}.json`;
                link.click();
                
                showAlert('Data exported successfully! 📤', 'success');
                
            } catch (error) {
                console.error('Error exporting data:', error);
                showAlert('Error exporting data', 'error');
            }
        }

        function createBackup() {
            
            exportData(); // Same as export for now
        }

        function cleanupData() {
            
            if (confirm('This will clean up old logs and optimize the database. Continue?')) {
                showAlert('Database cleanup completed! 🧹', 'success');
            }
        }

        function showAlert(message, type) {
            const existingAlerts = document.querySelectorAll('.alert');
            existingAlerts.forEach(alert => alert.remove());

            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            
            const mainContent = document.querySelector('.main-content');
            mainContent.insertBefore(alert, mainContent.firstChild);

            setTimeout(() => alert.remove(), 5000);
        }

        function showError(message) {
            showAlert(message, 'error');
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initializeSettings);
    </script>
</body>
</html>
